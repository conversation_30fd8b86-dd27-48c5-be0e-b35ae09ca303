"use client";

import { useEffect } from "react";

import { useRouter } from "next/navigation";

import { Users, UserPlus, Settings, Shield, User, Mail, CheckCircle, XCircle, Crown, Loader2 } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { useAuth } from "../_hooks/use-auth";

export function UserPage() {
  const { user, logout, isInitializing } = useAuth();
  const router = useRouter();

  // Handle redirect to login page when user is not authenticated
  useEffect(() => {
    if (!isInitializing && !user) {
      router.push("/auth/v1/login");
    }
  }, [isInitializing, user, router]);

  const handleLogout = async () => {
    await logout();
    router.push("/auth/v1/login");
  };

  // Show loading spinner during initialization
  if (isInitializing) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show loading or redirect message if not authenticated
  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">重定向到登录页面...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
          <p className="text-muted-foreground">管理用户、角色和权限</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <User className="h-3 w-3" />
            {user.username}
          </Badge>
          <Button variant="outline" onClick={handleLogout}>
            退出登录
          </Button>
        </div>
      </div>

      {/* User Info Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            当前用户资料
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <Label className="text-muted-foreground text-sm font-medium">用户名</Label>
              <div className="flex items-center gap-2">
                <User className="text-muted-foreground h-4 w-4" />
                <span className="font-medium">{user.username}</span>
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-muted-foreground text-sm font-medium">邮箱</Label>
              <div className="flex items-center gap-2">
                <Mail className="text-muted-foreground h-4 w-4" />
                <span className="font-medium">{user.email ?? "未设置"}</span>
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-muted-foreground text-sm font-medium">状态</Label>
              <div className="flex items-center gap-2">
                {user.is_active ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <Badge variant="outline" className="border-green-600 text-green-600">
                      活跃
                    </Badge>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-600" />
                    <Badge variant="outline" className="border-red-600 text-red-600">
                      非活跃
                    </Badge>
                  </>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-muted-foreground text-sm font-medium">Role</Label>
              <div className="flex items-center gap-2">
                {user.is_superuser ? (
                  <>
                    <Crown className="h-4 w-4 text-yellow-600" />
                    <Badge variant="outline" className="border-yellow-600 text-yellow-600">
                      Super Admin
                    </Badge>
                  </>
                ) : (
                  <>
                    <Shield className="h-4 w-4 text-blue-600" />
                    <Badge variant="outline" className="border-blue-600 text-blue-600">
                      User
                    </Badge>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList>
          <TabsTrigger value="users">User List</TabsTrigger>
          <TabsTrigger value="roles">Role Management</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="profile">Profile Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User List
              </CardTitle>
              <CardDescription>Manage system users and their access levels</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <Users className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">User Management</h3>
                <p className="text-muted-foreground mb-4">User list functionality will be implemented here</p>
                <Button disabled>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add User (Coming Soon)
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role Management
              </CardTitle>
              <CardDescription>Define and manage user roles and their capabilities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <Shield className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Role Management</h3>
                <p className="text-muted-foreground">Role management functionality will be implemented here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Permission Settings
              </CardTitle>
              <CardDescription>Configure detailed permissions for different user roles</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <Settings className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Permission Settings</h3>
                <p className="text-muted-foreground">Permission management functionality will be implemented here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Settings
              </CardTitle>
              <CardDescription>Update your personal information and account settings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <User className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Profile Settings</h3>
                <p className="text-muted-foreground">Profile management functionality will be implemented here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
