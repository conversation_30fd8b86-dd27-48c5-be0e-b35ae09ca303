"use client";

import { useState, useEffect } from "react";

import { useRouter } from "next/navigation";

import { <PERSON>ge<PERSON><PERSON><PERSON>, <PERSON>, LogOut, User } from "lucide-react";
import { toast } from "sonner";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { getInitials } from "@/lib/utils";

interface AuthUser {
  id: string;
  username: string;
  email?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000";

export function AccountSwitcher({
  users,
}: {
  readonly users: ReadonlyArray<{
    readonly id: string;
    readonly name: string;
    readonly email: string;
    readonly avatar: string;
    readonly role: string;
  }>;
}) {
  const [activeUser] = useState(users[0]);
  const [currentUser, setCurrentUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Load current user from token
  useEffect(() => {
    const token = localStorage.getItem("auth_token");
    if (token) {
      getCurrentUser(token);
    }
  }, []);

  const getCurrentUser = async (token: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/auth/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const user = await response.json();
        setCurrentUser(user);
      }
    } catch (error) {
      console.error("Failed to get current user:", error);
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem("auth_token");

      if (token) {
        await fetch(`${API_BASE_URL}/api/v1/auth/logout`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      }

      localStorage.removeItem("auth_token");
      toast.success("已成功退出登录");
      router.push("/auth/v1/login");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("退出登录失败");
    } finally {
      setIsLoading(false);
    }
  };

  // Use current user if available, otherwise fallback to static user
  const displayUser = currentUser
    ? {
        id: currentUser.id,
        name: currentUser.username,
        email: currentUser.email ?? "",
        avatar: "",
        role: currentUser.is_superuser ? "超级管理员" : "用户",
      }
    : activeUser;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="size-9 rounded-lg">
          <AvatarImage src={displayUser.avatar || undefined} alt={displayUser.name} />
          <AvatarFallback className="rounded-lg">{getInitials(displayUser.name)}</AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-56 space-y-1 rounded-lg" side="bottom" align="end" sideOffset={4}>
        <DropdownMenuItem className="p-0">
          <div className="flex w-full items-center justify-between gap-2 px-1 py-1.5">
            <Avatar className="size-9 rounded-lg">
              <AvatarImage src={displayUser.avatar || undefined} alt={displayUser.name} />
              <AvatarFallback className="rounded-lg">{getInitials(displayUser.name)}</AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">{displayUser.name}</span>
              <span className="truncate text-xs">{displayUser.role}</span>
            </div>
          </div>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => router.push("/user/default")}>
            <User />
            用户管理
          </DropdownMenuItem>
          <DropdownMenuItem>
            <BadgeCheck />
            账户设置
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Bell />
            通知
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout} disabled={isLoading}>
          <LogOut />
          {isLoading ? "退出中..." : "退出登录"}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
