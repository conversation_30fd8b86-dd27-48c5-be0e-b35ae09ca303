"use client";

import { useState, useEffect } from "react";

import { useRouter } from "next/navigation";

import { Plus, Briefcase, TrendingUp, TrendingDown, DollarSign, <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { useAuth } from "../_hooks/use-auth";
import { usePortfolios } from "../_hooks/use-portfolios";

import { CreatePortfolioForm } from "./create-portfolio-form";

// eslint-disable-next-line complexity
export function PortfolioPage() {
  const { user, isInitializing } = useAuth();
  const { portfolios, isLoading, createPortfolio, refreshPortfolios } = usePortfolios();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const router = useRouter();

  // Handle redirect to login page when user is not authenticated
  useEffect(() => {
    if (!isInitializing && !user) {
      router.push("/auth/v1/login");
    }
  }, [isInitializing, user, router]);

  useEffect(() => {
    if (user) {
      refreshPortfolios();
    }
  }, [user, refreshPortfolios]);

  // Show loading spinner during initialization
  if (isInitializing) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show loading or redirect message if not authenticated
  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">重定向到登录页面...</p>
          </div>
        </div>
      </div>
    );
  }

  const handleCreatePortfolio = async (data: any) => {
    try {
      await createPortfolio(data);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create portfolio:", error);
    }
  };

  // Calculate summary statistics
  const totalPortfolios = portfolios.length;
  const totalValue = portfolios.reduce((sum, p) => sum + parseFloat(p.statistics?.total_market_value ?? "0"), 0);
  const totalCost = portfolios.reduce((sum, p) => sum + parseFloat(p.statistics?.total_cost ?? "0"), 0);
  const totalProfitLoss = totalValue - totalCost;
  const totalReturnRate = totalCost > 0 ? (totalProfitLoss / totalCost) * 100 : 0;

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">投资组合管理</h1>
          <p className="text-muted-foreground">管理您的投资组合并跟踪业绩表现</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              创建投资组合
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>创建新投资组合</DialogTitle>
              <DialogDescription>创建一个新的投资组合来跟踪您的投资</DialogDescription>
            </DialogHeader>
            <CreatePortfolioForm onSubmit={handleCreatePortfolio} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Summary Cards */}
      <div className="mb-6 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">投资组合总数</CardTitle>
            <Briefcase className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPortfolios}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总市值</CardTitle>
            <DollarSign className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{totalValue.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总盈亏</CardTitle>
            {totalProfitLoss >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${totalProfitLoss >= 0 ? "text-green-600" : "text-red-600"}`}>
              ¥{totalProfitLoss.toLocaleString()}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">收益率</CardTitle>
            <PieChart className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${totalReturnRate >= 0 ? "text-green-600" : "text-red-600"}`}>
              {totalReturnRate.toFixed(2)}%
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              投资组合列表
            </CardTitle>
            <CardDescription>
              {portfolios.length === 0
                ? "您还没有创建任何投资组合。创建您的第一个投资组合开始投资之旅。"
                : `您共有 ${portfolios.length} 个投资组合。`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">加载投资组合中...</div>
              </div>
            ) : portfolios.length === 0 ? (
              <div className="py-8 text-center">
                <Briefcase className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">暂无投资组合</h3>
                <p className="text-muted-foreground mb-4">创建您的第一个投资组合开始跟踪您的投资</p>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  创建投资组合
                </Button>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {/* eslint-disable-next-line complexity */}
                {portfolios.map((portfolio) => (
                  <Card key={portfolio.id} className="cursor-pointer transition-shadow hover:shadow-md">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span className="truncate">{portfolio.name}</span>
                        <Badge variant="outline">{portfolio.strategy_type}</Badge>
                      </CardTitle>
                      <CardDescription className="line-clamp-2">{portfolio.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">初始资金:</span>
                          <span className="font-medium">¥{parseFloat(portfolio.initial_capital).toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">创建时间:</span>
                          <span className="font-medium">
                            {new Date(portfolio.created_at).toLocaleDateString("zh-CN")}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">投资策略:</span>
                          <span className="font-medium">{portfolio.strategy_type}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
