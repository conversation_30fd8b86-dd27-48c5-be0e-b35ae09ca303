"use client";

import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface CreatePortfolioFormProps {
  onSubmit: (data: {
    name: string;
    description?: string;
    strategy_type: string;
    initial_capital: number;
  }) => Promise<void>;
}

export function CreatePortfolioForm({ onSubmit }: CreatePortfolioFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    strategy_type: "指数投资",
    initial_capital: 0,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await onSubmit({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        strategy_type: formData.strategy_type,
        initial_capital: formData.initial_capital,
      });

      // Reset form
      setFormData({
        name: "",
        description: "",
        strategy_type: "指数投资",
        initial_capital: 0,
      });
    } catch (error) {
      console.error("Failed to create portfolio:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">投资组合名称 *</Label>
        <Input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
          placeholder="请输入投资组合名称"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
          placeholder="请输入投资组合描述（可选）"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="strategy_type">投资策略</Label>
        <Select
          value={formData.strategy_type}
          onValueChange={(value) => setFormData((prev) => ({ ...prev, strategy_type: value }))}
        >
          <SelectTrigger>
            <SelectValue placeholder="请选择投资策略" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="指数投资">指数投资</SelectItem>
            <SelectItem value="价值投资">价值投资</SelectItem>
            <SelectItem value="成长投资">成长投资</SelectItem>
            <SelectItem value="股息投资">股息投资</SelectItem>
            <SelectItem value="平衡配置">平衡配置</SelectItem>
            <SelectItem value="积极成长">积极成长</SelectItem>
            <SelectItem value="稳健保守">稳健保守</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="initial_capital">初始资金 (¥)</Label>
        <Input
          id="initial_capital"
          type="number"
          min="0"
          step="0.01"
          value={formData.initial_capital}
          onChange={(e) => setFormData((prev) => ({ ...prev, initial_capital: parseFloat(e.target.value) || 0 }))}
          placeholder="请输入初始资金金额"
        />
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="submit" disabled={isSubmitting || !formData.name.trim()}>
          {isSubmitting ? "创建中..." : "创建投资组合"}
        </Button>
      </div>
    </form>
  );
}
