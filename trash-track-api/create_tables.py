#!/usr/bin/env python3
"""
Create database tables and insert initial data
"""

import os
import sys
from sqlalchemy import create_engine, text
from app.core.database import Base
from app.models.user import User, UserSetting
from app.models.portfolio import Portfolio, Transaction, FundRecord, Position
from app.core.security import get_password_hash

# Database URL
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres123@localhost:5432/index_investing")

def create_tables():
    """Create all database tables"""
    print("Creating database tables...")
    
    engine = create_engine(DATABASE_URL)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    print("Tables created successfully!")
    
    # Insert initial data
    from sqlalchemy.orm import sessionmaker
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            # Create admin user
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                password_hash=get_password_hash("admin123"),
                is_active=True,
                is_superuser=True
            )
            db.add(admin_user)
            db.commit()
            print("Admin user created successfully!")
        else:
            print("Admin user already exists.")
            
        # Insert some sample indices data
        with engine.connect() as conn:
            # Check if indices exist
            result = conn.execute(text("SELECT COUNT(*) FROM indices"))
            count = result.scalar()
            
            if count == 0:
                # Insert sample indices
                indices_data = [
                    ('000001', '上证指数', 'SH', '综合指数'),
                    ('000300', '沪深300', 'SH', '宽基指数'),
                    ('000905', '中证500', 'SH', '中盘指数'),
                    ('399006', '创业板指', 'SZ', '成长指数'),
                    ('000016', '上证50', 'SH', '大盘指数')
                ]
                
                for code, name, market, category in indices_data:
                    conn.execute(text("""
                        INSERT INTO indices (code, name, market, category) 
                        VALUES (:code, :name, :market, :category)
                        ON CONFLICT (code) DO NOTHING
                    """), {
                        'code': code,
                        'name': name,
                        'market': market,
                        'category': category
                    })
                
                conn.commit()
                print("Sample indices data inserted successfully!")
            else:
                print("Indices data already exists.")
                
    except Exception as e:
        print(f"Error inserting initial data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_tables()
