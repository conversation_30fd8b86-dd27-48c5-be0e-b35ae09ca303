# Models package
from .user import User, UserSetting
from .security import Security, SecurityCategory, SecurityCategoryMapping, PriceHistory
from .xueqiu_portfolio import (
    XueqiuPortfolio,
    XueqiuPosition,
    XueqiuTransaction,
    XueqiuFundTransfer,
    XueqiuPositionRule
)

# 为了兼容性，保留旧的Portfolio模型别名
Portfolio = XueqiuPortfolio
Transaction = XueqiuTransaction
Position = XueqiuPosition
FundRecord = XueqiuFundTransfer

__all__ = [
    "User",
    "UserSetting",
    "Portfolio",
    "Transaction",
    "FundRecord",
    "Position",
    "Security",
    "SecurityCategory",
    "SecurityCategoryMapping",
    "PriceHistory",
    "XueqiuPortfolio",
    "XueqiuPosition",
    "XueqiuTransaction",
    "XueqiuFundTransfer",
    "XueqiuPositionRule"
]
