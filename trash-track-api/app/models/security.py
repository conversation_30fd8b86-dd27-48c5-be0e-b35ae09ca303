"""投资品种相关数据模型"""

from sqlalchemy import Column, String, Boolean, DECIMAL, BigInteger, Integer, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.core.database import Base


class Security(Base):
    """投资品种表"""
    __tablename__ = "securities"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    symbol = Column(String(20), unique=True, nullable=False, index=True)  # 如 SH513500, SZ159941
    name = Column(String(100), nullable=False)  # 如 标普500ETF, 纳指ETF
    market = Column(String(10), nullable=False, index=True)  # SH, SZ, HK, US
    category = Column(String(50))  # ETF, 股票, 基金等
    currency = Column(String(10), default='CNY')  # CNY, USD, HKD
    is_active = Column(Boolean, default=True)
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)
    updated_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    positions = relationship("Position", back_populates="security")
    transactions = relationship("Transaction", back_populates="security")
    price_history = relationship("PriceHistory", back_populates="security")
    category_mappings = relationship("SecurityCategoryMapping", back_populates="security")


class SecurityCategory(Base):
    """投资品种分类表"""
    __tablename__ = "security_categories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey('security_categories.id', ondelete='CASCADE'))
    level = Column(Integer, nullable=False)  # 1=一级, 2=二级, 3=三级
    sort_order = Column(Integer, default=0)
    description = Column(Text)
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)
    updated_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    parent = relationship("SecurityCategory", remote_side=[id], back_populates="children")
    children = relationship("SecurityCategory", back_populates="parent")
    security_mappings = relationship("SecurityCategoryMapping", back_populates="category")
    position_rules = relationship("PositionRule", back_populates="category")


class SecurityCategoryMapping(Base):
    """投资品种分类关联表"""
    __tablename__ = "security_category_mappings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    security_id = Column(UUID(as_uuid=True), ForeignKey('securities.id', ondelete='CASCADE'), nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey('security_categories.id', ondelete='CASCADE'), nullable=False)
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    security = relationship("Security", back_populates="category_mappings")
    category = relationship("SecurityCategory", back_populates="security_mappings")


class PriceHistory(Base):
    """价格历史表"""
    __tablename__ = "price_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    security_id = Column(UUID(as_uuid=True), ForeignKey('securities.id', ondelete='CASCADE'), nullable=False, index=True)
    price = Column(DECIMAL(10, 4), nullable=False)
    change_amount = Column(DECIMAL(10, 4), default=0)
    change_rate = Column(DECIMAL(8, 4), default=0)
    volume = Column(BigInteger, default=0)
    timestamp = Column(BigInteger, nullable=False, index=True)  # 价格时间戳
    source = Column(String(50), default='manual')  # 数据源
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    security = relationship("Security", back_populates="price_history")
