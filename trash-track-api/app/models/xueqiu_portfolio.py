"""雪球风格投资组合相关数据模型"""

from sqlalchemy import Column, String, Boolean, DECIMAL, BigInteger, Integer, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.core.database import Base


class XueqiuPortfolio(Base):
    """雪球风格投资组合表"""
    __tablename__ = "portfolios"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    strategy_type = Column(String(50))
    
    # 雪球风格的组合统计字段
    total_assets = Column(DECIMAL(15, 2), default=0)      # 总资产
    principal = Column(DECIMAL(15, 2), default=0)         # 本金
    cash = Column(DECIMAL(15, 2), default=0)              # 现金
    market_value = Column(DECIMAL(15, 2), default=0)      # 市值
    accum_amount = Column(DECIMAL(15, 2), default=0)      # 累计收益金额
    accum_rate = Column(DECIMAL(8, 4), default=0)         # 累计收益率
    day_float_amount = Column(DECIMAL(15, 2), default=0)  # 日浮动金额
    day_float_rate = Column(DECIMAL(8, 4), default=0)     # 日浮动率
    currency = Column(String(10), default='CNY')
    is_active = Column(Boolean, default=True)
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)
    updated_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    user = relationship("User", back_populates="portfolios")
    positions = relationship("XueqiuPosition", back_populates="portfolio", cascade="all, delete-orphan")
    transactions = relationship("XueqiuTransaction", back_populates="portfolio", cascade="all, delete-orphan")
    fund_transfers = relationship("XueqiuFundTransfer", back_populates="portfolio", cascade="all, delete-orphan")
    position_rules = relationship("XueqiuPositionRule", back_populates="portfolio", cascade="all, delete-orphan")


class XueqiuPosition(Base):
    """雪球风格持仓表"""
    __tablename__ = "positions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    portfolio_id = Column(UUID(as_uuid=True), ForeignKey('portfolios.id', ondelete='CASCADE'), nullable=False, index=True)
    security_id = Column(UUID(as_uuid=True), ForeignKey('securities.id', ondelete='CASCADE'), nullable=False, index=True)
    
    # 持仓基本信息
    shares = Column(DECIMAL(15, 2), nullable=False, default=0)           # 持股数量
    current_price = Column(DECIMAL(10, 4), default=0)                    # 当前价格
    change_amount = Column(DECIMAL(10, 4), default=0)                    # 价格变动金额
    change_rate = Column(DECIMAL(8, 4), default=0)                       # 价格变动率
    
    # 成本信息
    diluted_cost = Column(DECIMAL(10, 4), default=0)                     # 摊薄成本
    hold_cost = Column(DECIMAL(10, 4), default=0)                        # 持仓成本
    
    # 市值和收益
    market_value = Column(DECIMAL(15, 2), default=0)                     # 市值
    float_amount = Column(DECIMAL(15, 2), default=0)                     # 浮动盈亏金额
    float_rate = Column(DECIMAL(8, 4), default=0)                        # 浮动盈亏率
    accum_amount = Column(DECIMAL(15, 2), default=0)                     # 累计收益金额
    accum_rate = Column(DECIMAL(8, 4), default=0)                        # 累计收益率
    day_float_amount = Column(DECIMAL(15, 2), default=0)                 # 日浮动金额
    day_float_rate = Column(DECIMAL(8, 4), default=0)                    # 日浮动率
    
    # 时间信息
    open_time = Column(BigInteger)                                        # 开仓时间戳
    liquidation_time = Column(BigInteger, default=0)                     # 清仓时间戳（0表示未清仓）
    delay_remark = Column(String(255), default='')                       # 延迟备注
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)
    updated_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    portfolio = relationship("XueqiuPortfolio", back_populates="positions")
    security = relationship("Security", back_populates="positions")


class XueqiuTransaction(Base):
    """雪球风格交易记录表"""
    __tablename__ = "transactions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    portfolio_id = Column(UUID(as_uuid=True), ForeignKey('portfolios.id', ondelete='CASCADE'), nullable=False, index=True)
    security_id = Column(UUID(as_uuid=True), ForeignKey('securities.id', ondelete='CASCADE'), nullable=False, index=True)
    
    # 雪球原始字段
    tid = Column(BigInteger)                                              # 雪球交易ID
    transaction_type = Column(Integer, nullable=False)                   # 1=买入, 2=卖出, 9=除权除息
    type_name = Column(String(20), nullable=False)                       # 买入, 卖出, 除权除息
    shares = Column(DECIMAL(15, 2), nullable=False)                      # 交易股数
    price = Column(DECIMAL(10, 4), nullable=False)                       # 交易价格
    amount = Column(DECIMAL(15, 2), nullable=False)                      # 交易金额
    
    # 费用信息
    commission = Column(DECIMAL(10, 2))                                   # 佣金
    tax = Column(DECIMAL(10, 2))                                          # 税费
    commission_rate = Column(DECIMAL(8, 4))                               # 佣金率
    tax_rate = Column(DECIMAL(8, 4))                                      # 税率
    
    # 除权除息相关
    unit_dividend = Column(DECIMAL(10, 4))                                # 单位股息
    unit_shares = Column(DECIMAL(15, 2))                                  # 单位股数
    unit_increase_shares = Column(DECIMAL(15, 2))                         # 单位增股
    record_date = Column(BigInteger)                                      # 除权除息登记日
    
    # 其他信息
    comment = Column(Text)                                                # 备注
    description = Column(Text)                                            # 描述
    trade_time = Column(BigInteger, nullable=False, index=True)           # 交易时间戳
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)
    updated_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    portfolio = relationship("XueqiuPortfolio", back_populates="transactions")
    security = relationship("Security", back_populates="transactions")


class XueqiuFundTransfer(Base):
    """雪球风格转账记录表"""
    __tablename__ = "fund_transfers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    portfolio_id = Column(UUID(as_uuid=True), ForeignKey('portfolios.id', ondelete='CASCADE'), nullable=False, index=True)
    
    # 雪球原始字段
    tid = Column(BigInteger)                                              # 雪球转账ID
    transfer_type = Column(Integer, nullable=False)                      # 1=转入, 2=转出
    market = Column(String(10), nullable=False)                          # CHA, US, HK
    amount = Column(DECIMAL(15, 2), nullable=False)                      # 转账金额
    transfer_time = Column(BigInteger, nullable=False, index=True)        # 转账时间戳
    note = Column(Text)                                                   # 备注
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)
    updated_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    portfolio = relationship("XueqiuPortfolio", back_populates="fund_transfers")


class XueqiuPositionRule(Base):
    """雪球风格仓位管理规则表"""
    __tablename__ = "position_rules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    portfolio_id = Column(UUID(as_uuid=True), ForeignKey('portfolios.id', ondelete='CASCADE'), nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey('security_categories.id', ondelete='CASCADE'), nullable=False)
    min_weight = Column(DECIMAL(5, 4), default=0)                        # 最小仓位权重 (0-1)
    max_weight = Column(DECIMAL(5, 4), default=1)                        # 最大仓位权重 (0-1)
    target_weight = Column(DECIMAL(5, 4))                                 # 目标仓位权重 (0-1)
    is_active = Column(Boolean, default=True)
    created_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)
    updated_at = Column(BigInteger, server_default=func.extract('epoch', func.now()) * 1000)

    # 关系
    portfolio = relationship("XueqiuPortfolio", back_populates="position_rules")
    category = relationship("SecurityCategory", back_populates="position_rules")
