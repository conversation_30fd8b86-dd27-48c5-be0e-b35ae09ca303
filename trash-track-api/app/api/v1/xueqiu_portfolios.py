"""雪球风格投资组合API"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc

from app.core.database import get_db
from app.models.xueqiu_portfolio import (
    XueqiuPortfolio, XueqiuPosition, XueqiuTransaction, XueqiuFundTransfer, XueqiuPositionRule
)
from app.models.security import Security
from app.schemas.xueqiu_portfolio import (
    XueqiuPortfolioResponse, XueqiuPortfolioCreate, XueqiuPortfolioUpdate,
    XueqiuPositionResponse, XueqiuTransactionResponse, XueqiuTransactionCreate,
    XueqiuFundTransferResponse, XueqiuFundTransferCreate
)
from ...api.deps import get_current_user
from app.models.user import User

router = APIRouter()


@router.get("/", response_model=List[XueqiuPortfolioResponse])
async def get_portfolios(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户的投资组合列表"""
    portfolios = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.user_id == current_user.id,
                XueqiuPortfolio.is_active == True
            )
        )
        .options(joinedload(XueqiuPortfolio.positions))
        .offset(skip)
        .limit(limit)
        .all()
    )
    return portfolios


@router.get("/{portfolio_id}", response_model=XueqiuPortfolioResponse)
async def get_portfolio(
    portfolio_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个投资组合详情"""
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .options(
            joinedload(XueqiuPortfolio.positions).joinedload(XueqiuPosition.security),
            joinedload(XueqiuPortfolio.transactions).joinedload(XueqiuTransaction.security),
            joinedload(XueqiuPortfolio.fund_transfers)
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    return portfolio


@router.post("/", response_model=XueqiuPortfolioResponse)
async def create_portfolio(
    portfolio_data: XueqiuPortfolioCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建投资组合"""
    portfolio = XueqiuPortfolio(
        user_id=current_user.id,
        **portfolio_data.dict()
    )
    db.add(portfolio)
    db.commit()
    db.refresh(portfolio)
    return portfolio


@router.put("/{portfolio_id}", response_model=XueqiuPortfolioResponse)
async def update_portfolio(
    portfolio_id: str,
    portfolio_data: XueqiuPortfolioUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新投资组合"""
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    for field, value in portfolio_data.dict(exclude_unset=True).items():
        setattr(portfolio, field, value)
    
    db.commit()
    db.refresh(portfolio)
    return portfolio


@router.delete("/{portfolio_id}")
async def delete_portfolio(
    portfolio_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除投资组合"""
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    portfolio.is_active = False
    db.commit()
    return {"message": "投资组合已删除"}


@router.get("/{portfolio_id}/positions", response_model=List[XueqiuPositionResponse])
async def get_portfolio_positions(
    portfolio_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资组合持仓"""
    # 验证组合权限
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    positions = (
        db.query(XueqiuPosition)
        .filter(XueqiuPosition.portfolio_id == portfolio_id)
        .options(joinedload(XueqiuPosition.security))
        .all()
    )
    
    return positions


@router.get("/{portfolio_id}/transactions", response_model=List[XueqiuTransactionResponse])
async def get_portfolio_transactions(
    portfolio_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    transaction_type: Optional[int] = Query(None, description="交易类型：1=买入, 2=卖出, 9=除权除息"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资组合交易记录"""
    # 验证组合权限
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    query = (
        db.query(XueqiuTransaction)
        .filter(XueqiuTransaction.portfolio_id == portfolio_id)
        .options(joinedload(XueqiuTransaction.security))
    )
    
    if transaction_type:
        query = query.filter(XueqiuTransaction.transaction_type == transaction_type)
    
    transactions = (
        query.order_by(desc(XueqiuTransaction.trade_time))
        .offset(skip)
        .limit(limit)
        .all()
    )
    
    return transactions


@router.post("/{portfolio_id}/transactions", response_model=XueqiuTransactionResponse)
async def create_transaction(
    portfolio_id: str,
    transaction_data: XueqiuTransactionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建交易记录"""
    # 验证组合权限
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    # 验证投资品种是否存在
    security = db.query(Security).filter(Security.id == transaction_data.security_id).first()
    if not security:
        raise HTTPException(status_code=404, detail="投资品种不存在")
    
    transaction = XueqiuTransaction(
        portfolio_id=portfolio_id,
        **transaction_data.dict()
    )
    db.add(transaction)
    db.commit()
    db.refresh(transaction)
    return transaction


@router.get("/{portfolio_id}/fund-transfers", response_model=List[XueqiuFundTransferResponse])
async def get_portfolio_fund_transfers(
    portfolio_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    transfer_type: Optional[int] = Query(None, description="转账类型：1=转入, 2=转出"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资组合转账记录"""
    # 验证组合权限
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    query = db.query(XueqiuFundTransfer).filter(XueqiuFundTransfer.portfolio_id == portfolio_id)
    
    if transfer_type:
        query = query.filter(XueqiuFundTransfer.transfer_type == transfer_type)
    
    transfers = (
        query.order_by(desc(XueqiuFundTransfer.transfer_time))
        .offset(skip)
        .limit(limit)
        .all()
    )
    
    return transfers


@router.post("/{portfolio_id}/fund-transfers", response_model=XueqiuFundTransferResponse)
async def create_fund_transfer(
    portfolio_id: str,
    transfer_data: XueqiuFundTransferCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建转账记录"""
    # 验证组合权限
    portfolio = (
        db.query(XueqiuPortfolio)
        .filter(
            and_(
                XueqiuPortfolio.id == portfolio_id,
                XueqiuPortfolio.user_id == current_user.id
            )
        )
        .first()
    )
    
    if not portfolio:
        raise HTTPException(status_code=404, detail="投资组合不存在")
    
    transfer = XueqiuFundTransfer(
        portfolio_id=portfolio_id,
        **transfer_data.dict()
    )
    db.add(transfer)
    db.commit()
    db.refresh(transfer)
    return transfer
