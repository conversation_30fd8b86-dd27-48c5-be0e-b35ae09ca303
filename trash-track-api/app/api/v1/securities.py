"""投资品种相关API"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.database import get_db
from app.models.security import Security, SecurityCategory, SecurityCategoryMapping, PriceHistory
from app.schemas.security import (
    SecurityResponse, SecurityCreate, SecurityUpdate,
    SecurityCategoryResponse, SecurityCategoryCreate,
    PriceHistoryResponse, PriceHistoryCreate
)
from ...api.deps import get_current_user
from app.models.user import User

router = APIRouter()


@router.get("/", response_model=List[SecurityResponse])
async def get_securities(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    market: Optional[str] = Query(None, description="市场代码：SH, SZ, HK, US"),
    category: Optional[str] = Query(None, description="品种类别"),
    search: Optional[str] = Query(None, description="搜索关键词（代码或名称）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资品种列表"""
    query = db.query(Security).filter(Security.is_active == True)
    
    if market:
        query = query.filter(Security.market == market)
    
    if category:
        query = query.filter(Security.category == category)
    
    if search:
        query = query.filter(
            or_(
                Security.symbol.ilike(f"%{search}%"),
                Security.name.ilike(f"%{search}%")
            )
        )
    
    securities = query.offset(skip).limit(limit).all()
    return securities


@router.get("/{security_id}", response_model=SecurityResponse)
async def get_security(
    security_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个投资品种详情"""
    security = db.query(Security).filter(Security.id == security_id).first()
    if not security:
        raise HTTPException(status_code=404, detail="投资品种不存在")
    return security


@router.post("/", response_model=SecurityResponse)
async def create_security(
    security_data: SecurityCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建投资品种"""
    # 检查代码是否已存在
    existing = db.query(Security).filter(Security.symbol == security_data.symbol).first()
    if existing:
        raise HTTPException(status_code=400, detail="投资品种代码已存在")
    
    security = Security(**security_data.dict())
    db.add(security)
    db.commit()
    db.refresh(security)
    return security


@router.put("/{security_id}", response_model=SecurityResponse)
async def update_security(
    security_id: str,
    security_data: SecurityUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新投资品种"""
    security = db.query(Security).filter(Security.id == security_id).first()
    if not security:
        raise HTTPException(status_code=404, detail="投资品种不存在")
    
    for field, value in security_data.dict(exclude_unset=True).items():
        setattr(security, field, value)
    
    db.commit()
    db.refresh(security)
    return security


@router.delete("/{security_id}")
async def delete_security(
    security_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除投资品种"""
    security = db.query(Security).filter(Security.id == security_id).first()
    if not security:
        raise HTTPException(status_code=404, detail="投资品种不存在")
    
    security.is_active = False
    db.commit()
    return {"message": "投资品种已删除"}


@router.get("/categories/", response_model=List[SecurityCategoryResponse])
async def get_security_categories(
    level: Optional[int] = Query(None, description="分类级别：1, 2, 3"),
    parent_id: Optional[str] = Query(None, description="父分类ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资品种分类"""
    query = db.query(SecurityCategory)
    
    if level:
        query = query.filter(SecurityCategory.level == level)
    
    if parent_id:
        query = query.filter(SecurityCategory.parent_id == parent_id)
    
    categories = query.order_by(SecurityCategory.level, SecurityCategory.sort_order).all()
    return categories


@router.post("/categories/", response_model=SecurityCategoryResponse)
async def create_security_category(
    category_data: SecurityCategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建投资品种分类"""
    category = SecurityCategory(**category_data.dict())
    db.add(category)
    db.commit()
    db.refresh(category)
    return category


@router.get("/{security_id}/price-history", response_model=List[PriceHistoryResponse])
async def get_price_history(
    security_id: str,
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资品种价格历史"""
    price_history = (
        db.query(PriceHistory)
        .filter(PriceHistory.security_id == security_id)
        .order_by(PriceHistory.timestamp.desc())
        .limit(limit)
        .all()
    )
    return price_history


@router.post("/{security_id}/price-history", response_model=PriceHistoryResponse)
async def create_price_history(
    security_id: str,
    price_data: PriceHistoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """添加价格历史记录"""
    # 验证投资品种是否存在
    security = db.query(Security).filter(Security.id == security_id).first()
    if not security:
        raise HTTPException(status_code=404, detail="投资品种不存在")
    
    price_history = PriceHistory(
        security_id=security_id,
        **price_data.dict()
    )
    db.add(price_history)
    db.commit()
    db.refresh(price_history)
    return price_history
