from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid
import io
import csv
from datetime import datetime
from ...core.database import get_db
from ...schemas.portfolio import (
    Portfolio, PortfolioCreate, PortfolioUpdate, PortfolioDetail,
    Transaction, TransactionCreate, TransactionUpdate,
    FundRecord, FundRecordCreate,
    Position, PortfolioStatistics
)
from ...schemas.user import User
from ...services.portfolio_service import PortfolioService
from ...api.deps import get_current_user

router = APIRouter()


@router.get("/", response_model=List[Portfolio])
async def get_portfolios(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's portfolios"""
    portfolio_service = PortfolioService(db)
    return portfolio_service.get_user_portfolios(current_user.id, skip=skip, limit=limit)


@router.post("/", response_model=Portfolio)
async def create_portfolio(
    portfolio_create: PortfolioCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create new portfolio"""
    portfolio_service = PortfolioService(db)
    return portfolio_service.create_portfolio(current_user.id, portfolio_create)


@router.get("/{portfolio_id}", response_model=PortfolioDetail)
async def get_portfolio(
    portfolio_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get portfolio details"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Get related data
    transactions = portfolio_service.get_transactions(portfolio_id)
    fund_records = portfolio_service.get_fund_records(portfolio_id)
    positions = portfolio_service.get_positions(portfolio_id)
    statistics = portfolio_service.calculate_portfolio_statistics(portfolio_id)
    
    return PortfolioDetail(
        **portfolio.__dict__,
        transactions=transactions,
        fund_records=fund_records,
        positions=positions,
        statistics=statistics
    )


@router.put("/{portfolio_id}", response_model=Portfolio)
async def update_portfolio(
    portfolio_id: uuid.UUID,
    portfolio_update: PortfolioUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update portfolio"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    updated_portfolio = portfolio_service.update_portfolio(portfolio_id, portfolio_update)
    return updated_portfolio


@router.delete("/{portfolio_id}")
async def delete_portfolio(
    portfolio_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete portfolio"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    portfolio_service.delete_portfolio(portfolio_id)
    return {"message": "Portfolio deleted successfully"}


@router.get("/{portfolio_id}/transactions", response_model=List[Transaction])
async def get_transactions(
    portfolio_id: uuid.UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get portfolio transactions"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return portfolio_service.get_transactions(portfolio_id, skip=skip, limit=limit)


@router.post("/{portfolio_id}/transactions", response_model=Transaction)
async def create_transaction(
    portfolio_id: uuid.UUID,
    transaction_create: TransactionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create new transaction"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return portfolio_service.create_transaction(portfolio_id, transaction_create)


@router.put("/{portfolio_id}/transactions/{transaction_id}", response_model=Transaction)
async def update_transaction(
    portfolio_id: uuid.UUID,
    transaction_id: uuid.UUID,
    transaction_update: TransactionUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update transaction"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    transaction = portfolio_service.update_transaction(transaction_id, transaction_update)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    return transaction


@router.delete("/{portfolio_id}/transactions/{transaction_id}")
async def delete_transaction(
    portfolio_id: uuid.UUID,
    transaction_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete transaction"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    if not portfolio_service.delete_transaction(transaction_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    return {"message": "Transaction deleted successfully"}


@router.get("/{portfolio_id}/fund-records", response_model=List[FundRecord])
async def get_fund_records(
    portfolio_id: uuid.UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get portfolio fund records"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return portfolio_service.get_fund_records(portfolio_id, skip=skip, limit=limit)


@router.post("/{portfolio_id}/fund-records", response_model=FundRecord)
async def create_fund_record(
    portfolio_id: uuid.UUID,
    fund_record_create: FundRecordCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create new fund record"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return portfolio_service.create_fund_record(portfolio_id, fund_record_create)


@router.get("/{portfolio_id}/positions", response_model=List[Position])
async def get_positions(
    portfolio_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get portfolio positions"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return portfolio_service.get_positions(portfolio_id)


@router.get("/{portfolio_id}/statistics", response_model=PortfolioStatistics)
async def get_portfolio_statistics(
    portfolio_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get portfolio statistics"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return portfolio_service.calculate_portfolio_statistics(portfolio_id)


@router.post("/{portfolio_id}/transactions/import")
async def import_transactions(
    portfolio_id: uuid.UUID,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Import transactions from CSV file"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )

    # Check ownership
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

    # Check file type
    if not file.filename.endswith('.csv'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only CSV files are supported"
        )

    try:
        # Read CSV file
        content = await file.read()
        csv_content = content.decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_content))

        imported_count = 0
        errors = []

        for row_num, row in enumerate(csv_reader, start=2):  # Start from 2 because row 1 is header
            try:
                # Parse CSV row
                transaction_data = {
                    'index_code': row.get('index_code', '').strip(),
                    'index_name': row.get('index_name', '').strip(),
                    'transaction_type': row.get('transaction_type', '').strip().lower(),
                    'price': float(row.get('price', 0)),
                    'quantity': int(row.get('quantity', 0)),
                    'fee': float(row.get('fee', 0)),
                    'tax': float(row.get('tax', 0)),
                    'total_amount': float(row.get('total_amount', 0)),
                    'note': row.get('note', '').strip(),
                    'trade_date': datetime.strptime(row.get('trade_date', ''), '%Y-%m-%d').date()
                }

                # Validate required fields
                if not transaction_data['index_code']:
                    errors.append(f"Row {row_num}: index_code is required")
                    continue

                if transaction_data['transaction_type'] not in ['buy', 'sell', 'dividend', 'split']:
                    errors.append(f"Row {row_num}: invalid transaction_type")
                    continue

                # Create transaction
                transaction_create = TransactionCreate(**transaction_data)
                portfolio_service.create_transaction(portfolio_id, transaction_create)
                imported_count += 1

            except Exception as e:
                errors.append(f"Row {row_num}: {str(e)}")

        return {
            "message": f"Import completed. {imported_count} transactions imported.",
            "imported_count": imported_count,
            "errors": errors
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error processing CSV file: {str(e)}"
        )
