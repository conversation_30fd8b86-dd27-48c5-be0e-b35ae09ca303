"""雪球风格投资组合相关的Pydantic schemas"""

from typing import Optional, List
from pydantic import BaseModel, Field
from decimal import Decimal
import uuid

from .security import SecurityResponse


class XueqiuPortfolioBase(BaseModel):
    """雪球风格投资组合基础模型"""
    name: str = Field(..., description="组合名称", max_length=100)
    description: Optional[str] = Field(None, description="组合描述")
    strategy_type: Optional[str] = Field(None, description="投资策略", max_length=50)
    currency: str = Field("CNY", description="货币", max_length=10)


class XueqiuPortfolioCreate(XueqiuPortfolioBase):
    """创建雪球风格投资组合"""
    principal: Decimal = Field(0, description="本金", decimal_places=2)
    cash: Decimal = Field(0, description="现金", decimal_places=2)


class XueqiuPortfolioUpdate(BaseModel):
    """更新雪球风格投资组合"""
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    strategy_type: Optional[str] = Field(None, max_length=50)
    total_assets: Optional[Decimal] = Field(None, decimal_places=2)
    principal: Optional[Decimal] = Field(None, decimal_places=2)
    cash: Optional[Decimal] = Field(None, decimal_places=2)
    market_value: Optional[Decimal] = Field(None, decimal_places=2)
    accum_amount: Optional[Decimal] = Field(None, decimal_places=2)
    accum_rate: Optional[Decimal] = Field(None, decimal_places=4)
    day_float_amount: Optional[Decimal] = Field(None, decimal_places=2)
    day_float_rate: Optional[Decimal] = Field(None, decimal_places=4)
    is_active: Optional[bool] = None


class XueqiuPositionBase(BaseModel):
    """雪球风格持仓基础模型"""
    shares: Decimal = Field(..., description="持股数量", decimal_places=2)
    current_price: Decimal = Field(0, description="当前价格", decimal_places=4)
    diluted_cost: Decimal = Field(0, description="摊薄成本", decimal_places=4)
    hold_cost: Decimal = Field(0, description="持仓成本", decimal_places=4)


class XueqiuPositionResponse(XueqiuPositionBase):
    """雪球风格持仓响应"""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    security_id: uuid.UUID
    security: Optional[SecurityResponse] = None
    change_amount: Decimal
    change_rate: Decimal
    market_value: Decimal
    float_amount: Decimal
    float_rate: Decimal
    accum_amount: Decimal
    accum_rate: Decimal
    day_float_amount: Decimal
    day_float_rate: Decimal
    open_time: Optional[int] = None
    liquidation_time: int
    delay_remark: str
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True


class XueqiuTransactionBase(BaseModel):
    """雪球风格交易记录基础模型"""
    security_id: uuid.UUID = Field(..., description="投资品种ID")
    transaction_type: int = Field(..., description="交易类型：1=买入, 2=卖出, 9=除权除息")
    type_name: str = Field(..., description="交易类型名称", max_length=20)
    shares: Decimal = Field(..., description="交易股数", decimal_places=2)
    price: Decimal = Field(..., description="交易价格", decimal_places=4)
    amount: Decimal = Field(..., description="交易金额", decimal_places=2)
    trade_time: int = Field(..., description="交易时间戳")


class XueqiuTransactionCreate(XueqiuTransactionBase):
    """创建雪球风格交易记录"""
    tid: Optional[int] = Field(None, description="雪球交易ID")
    commission: Optional[Decimal] = Field(None, description="佣金", decimal_places=2)
    tax: Optional[Decimal] = Field(None, description="税费", decimal_places=2)
    commission_rate: Optional[Decimal] = Field(None, description="佣金率", decimal_places=4)
    tax_rate: Optional[Decimal] = Field(None, description="税率", decimal_places=4)
    unit_dividend: Optional[Decimal] = Field(None, description="单位股息", decimal_places=4)
    unit_shares: Optional[Decimal] = Field(None, description="单位股数", decimal_places=2)
    unit_increase_shares: Optional[Decimal] = Field(None, description="单位增股", decimal_places=2)
    record_date: Optional[int] = Field(None, description="除权除息登记日")
    comment: Optional[str] = Field(None, description="备注")
    description: Optional[str] = Field(None, description="描述")


class XueqiuTransactionResponse(XueqiuTransactionBase):
    """雪球风格交易记录响应"""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    security: Optional[SecurityResponse] = None
    tid: Optional[int] = None
    commission: Optional[Decimal] = None
    tax: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None
    tax_rate: Optional[Decimal] = None
    unit_dividend: Optional[Decimal] = None
    unit_shares: Optional[Decimal] = None
    unit_increase_shares: Optional[Decimal] = None
    record_date: Optional[int] = None
    comment: Optional[str] = None
    description: Optional[str] = None
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True


class XueqiuFundTransferBase(BaseModel):
    """雪球风格转账记录基础模型"""
    transfer_type: int = Field(..., description="转账类型：1=转入, 2=转出")
    market: str = Field(..., description="市场", max_length=10)
    amount: Decimal = Field(..., description="转账金额", decimal_places=2)
    transfer_time: int = Field(..., description="转账时间戳")


class XueqiuFundTransferCreate(XueqiuFundTransferBase):
    """创建雪球风格转账记录"""
    tid: Optional[int] = Field(None, description="雪球转账ID")
    note: Optional[str] = Field(None, description="备注")


class XueqiuFundTransferResponse(XueqiuFundTransferBase):
    """雪球风格转账记录响应"""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    tid: Optional[int] = None
    note: Optional[str] = None
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True


class XueqiuPortfolioResponse(XueqiuPortfolioBase):
    """雪球风格投资组合响应"""
    id: uuid.UUID
    user_id: uuid.UUID
    total_assets: Decimal
    principal: Decimal
    cash: Decimal
    market_value: Decimal
    accum_amount: Decimal
    accum_rate: Decimal
    day_float_amount: Decimal
    day_float_rate: Decimal
    is_active: bool
    created_at: int
    updated_at: int
    positions: Optional[List[XueqiuPositionResponse]] = []
    transactions: Optional[List[XueqiuTransactionResponse]] = []
    fund_transfers: Optional[List[XueqiuFundTransferResponse]] = []

    class Config:
        from_attributes = True
