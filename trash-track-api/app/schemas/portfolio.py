from pydantic import BaseModel, ConfigDict
from typing import Optional, List
from datetime import datetime, date
from decimal import Decimal
import uuid


class PortfolioBase(BaseModel):
    name: str
    description: Optional[str] = None
    strategy_type: Optional[str] = None
    initial_capital: Optional[Decimal] = Decimal('0')


class PortfolioCreate(PortfolioBase):
    pass


class PortfolioUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    strategy_type: Optional[str] = None
    initial_capital: Optional[Decimal] = None


class Portfolio(PortfolioBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    user_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class TransactionBase(BaseModel):
    index_code: str
    index_name: Optional[str] = None
    transaction_type: str  # 'buy', 'sell', 'dividend', 'split'
    price: Decimal
    quantity: int
    fee: Optional[Decimal] = Decimal('0')
    tax: Optional[Decimal] = Decimal('0')
    total_amount: Decimal
    note: Optional[str] = None
    trade_date: date


class TransactionCreate(TransactionBase):
    pass


class TransactionUpdate(BaseModel):
    index_code: Optional[str] = None
    index_name: Optional[str] = None
    transaction_type: Optional[str] = None
    price: Optional[Decimal] = None
    quantity: Optional[int] = None
    fee: Optional[Decimal] = None
    tax: Optional[Decimal] = None
    total_amount: Optional[Decimal] = None
    note: Optional[str] = None
    trade_date: Optional[date] = None


class Transaction(TransactionBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    portfolio_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class FundRecordBase(BaseModel):
    record_type: str  # 'deposit', 'withdrawal'
    amount: Decimal
    note: Optional[str] = None
    record_date: date


class FundRecordCreate(FundRecordBase):
    pass


class FundRecord(FundRecordBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    portfolio_id: uuid.UUID
    created_at: datetime


class PositionBase(BaseModel):
    index_code: str
    index_name: Optional[str] = None
    quantity: int = 0
    avg_cost: Decimal = Decimal('0')
    total_cost: Decimal = Decimal('0')


class Position(PositionBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    portfolio_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class PortfolioStatistics(BaseModel):
    total_market_value: Decimal
    total_cost: Decimal
    total_profit_loss: Decimal
    total_return_rate: Decimal
    available_cash: Decimal
    total_assets: Decimal
    position_count: int


class PortfolioDetail(Portfolio):
    transactions: List[Transaction] = []
    fund_records: List[FundRecord] = []
    positions: List[Position] = []
    statistics: Optional[PortfolioStatistics] = None
