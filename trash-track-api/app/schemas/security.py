"""投资品种相关的Pydantic schemas"""

from typing import Optional, List
from pydantic import BaseModel, Field
from decimal import Decimal
import uuid


class SecurityBase(BaseModel):
    """投资品种基础模型"""
    symbol: str = Field(..., description="投资品种代码", max_length=20)
    name: str = Field(..., description="投资品种名称", max_length=100)
    market: str = Field(..., description="市场代码", max_length=10)
    category: Optional[str] = Field(None, description="品种类别", max_length=50)
    currency: str = Field("CNY", description="货币", max_length=10)


class SecurityCreate(SecurityBase):
    """创建投资品种"""
    pass


class SecurityUpdate(BaseModel):
    """更新投资品种"""
    symbol: Optional[str] = Field(None, max_length=20)
    name: Optional[str] = Field(None, max_length=100)
    market: Optional[str] = Field(None, max_length=10)
    category: Optional[str] = Field(None, max_length=50)
    currency: Optional[str] = Field(None, max_length=10)
    is_active: Optional[bool] = None


class SecurityResponse(SecurityBase):
    """投资品种响应"""
    id: uuid.UUID
    is_active: bool
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True


class SecurityCategoryBase(BaseModel):
    """投资品种分类基础模型"""
    name: str = Field(..., description="分类名称", max_length=100)
    parent_id: Optional[uuid.UUID] = Field(None, description="父分类ID")
    level: int = Field(..., description="分类级别", ge=1, le=3)
    sort_order: int = Field(0, description="排序")
    description: Optional[str] = Field(None, description="描述")


class SecurityCategoryCreate(SecurityCategoryBase):
    """创建投资品种分类"""
    pass


class SecurityCategoryResponse(SecurityCategoryBase):
    """投资品种分类响应"""
    id: uuid.UUID
    created_at: int
    updated_at: int
    children: Optional[List['SecurityCategoryResponse']] = []

    class Config:
        from_attributes = True


class SecurityCategoryMappingBase(BaseModel):
    """投资品种分类关联基础模型"""
    security_id: uuid.UUID
    category_id: uuid.UUID


class SecurityCategoryMappingCreate(SecurityCategoryMappingBase):
    """创建投资品种分类关联"""
    pass


class SecurityCategoryMappingResponse(SecurityCategoryMappingBase):
    """投资品种分类关联响应"""
    id: uuid.UUID
    created_at: int

    class Config:
        from_attributes = True


class PriceHistoryBase(BaseModel):
    """价格历史基础模型"""
    price: Decimal = Field(..., description="价格", decimal_places=4)
    change_amount: Decimal = Field(0, description="价格变动金额", decimal_places=4)
    change_rate: Decimal = Field(0, description="价格变动率", decimal_places=4)
    volume: int = Field(0, description="成交量")
    timestamp: int = Field(..., description="时间戳")
    source: str = Field("manual", description="数据源", max_length=50)


class PriceHistoryCreate(PriceHistoryBase):
    """创建价格历史"""
    pass


class PriceHistoryResponse(PriceHistoryBase):
    """价格历史响应"""
    id: uuid.UUID
    security_id: uuid.UUID
    created_at: int

    class Config:
        from_attributes = True


# 解决前向引用问题
SecurityCategoryResponse.model_rebuild()
