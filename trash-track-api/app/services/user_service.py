from sqlalchemy.orm import Session
from typing import Optional, List
from ..models.user import User, UserSetting
from ..schemas.user import UserCreate, UserUpdate, UserSettingCreate, UserSettingUpdate
from ..core.security import get_password_hash, verify_password
import uuid


class UserService:
    def __init__(self, db: Session):
        self.db = db

    def get_user(self, user_id: uuid.UUID) -> Optional[User]:
        """Get user by ID"""
        return self.db.query(User).filter(User.id == user_id).first()

    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        return self.db.query(User).filter(User.username == username).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return self.db.query(User).filter(User.email == email).first()

    def get_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get list of users"""
        return self.db.query(User).offset(skip).limit(limit).all()

    def create_user(self, user_create: UserCreate) -> User:
        """Create new user"""
        # Check if username already exists
        if self.get_user_by_username(user_create.username):
            raise ValueError("Username already exists")
        
        # Check if email already exists
        if user_create.email and self.get_user_by_email(user_create.email):
            raise ValueError("Email already exists")

        # Create user
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            password_hash=get_password_hash(user_create.password),
            is_active=user_create.is_active
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        return db_user

    def update_user(self, user_id: uuid.UUID, user_update: UserUpdate) -> Optional[User]:
        """Update user"""
        db_user = self.get_user(user_id)
        if not db_user:
            return None

        update_data = user_update.model_dump(exclude_unset=True)
        
        # Handle password update
        if "password" in update_data:
            update_data["password_hash"] = get_password_hash(update_data.pop("password"))

        # Check username uniqueness
        if "username" in update_data:
            existing_user = self.get_user_by_username(update_data["username"])
            if existing_user and existing_user.id != user_id:
                raise ValueError("Username already exists")

        # Check email uniqueness
        if "email" in update_data:
            existing_user = self.get_user_by_email(update_data["email"])
            if existing_user and existing_user.id != user_id:
                raise ValueError("Email already exists")

        for field, value in update_data.items():
            setattr(db_user, field, value)

        self.db.commit()
        self.db.refresh(db_user)
        return db_user

    def delete_user(self, user_id: uuid.UUID) -> bool:
        """Delete user"""
        db_user = self.get_user(user_id)
        if not db_user:
            return False

        self.db.delete(db_user)
        self.db.commit()
        return True

    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user"""
        user = self.get_user_by_username(username)
        if not user:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user

    def get_user_settings(self, user_id: uuid.UUID) -> List[UserSetting]:
        """Get user settings"""
        return self.db.query(UserSetting).filter(UserSetting.user_id == user_id).all()

    def get_user_setting(self, user_id: uuid.UUID, setting_key: str) -> Optional[UserSetting]:
        """Get specific user setting"""
        return self.db.query(UserSetting).filter(
            UserSetting.user_id == user_id,
            UserSetting.setting_key == setting_key
        ).first()

    def create_user_setting(self, user_id: uuid.UUID, setting_create: UserSettingCreate) -> UserSetting:
        """Create user setting"""
        # Check if setting already exists
        existing_setting = self.get_user_setting(user_id, setting_create.setting_key)
        if existing_setting:
            raise ValueError("Setting already exists")

        db_setting = UserSetting(
            user_id=user_id,
            setting_key=setting_create.setting_key,
            setting_value=setting_create.setting_value
        )
        
        self.db.add(db_setting)
        self.db.commit()
        self.db.refresh(db_setting)
        return db_setting

    def update_user_setting(self, user_id: uuid.UUID, setting_key: str, setting_update: UserSettingUpdate) -> Optional[UserSetting]:
        """Update user setting"""
        db_setting = self.get_user_setting(user_id, setting_key)
        if not db_setting:
            return None

        update_data = setting_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_setting, field, value)

        self.db.commit()
        self.db.refresh(db_setting)
        return db_setting

    def delete_user_setting(self, user_id: uuid.UUID, setting_key: str) -> bool:
        """Delete user setting"""
        db_setting = self.get_user_setting(user_id, setting_key)
        if not db_setting:
            return False

        self.db.delete(db_setting)
        self.db.commit()
        return True
