from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from typing import Optional, List
from decimal import Decimal
from datetime import date
import uuid
from ..models import Portfolio, Transaction, FundRecord, Position
from ..schemas.portfolio import (
    PortfolioCreate, PortfolioUpdate, TransactionCreate, TransactionUpdate,
    FundRecordCreate, PortfolioStatistics
)


class PortfolioService:
    def __init__(self, db: Session):
        self.db = db

    def get_portfolio(self, portfolio_id: uuid.UUID) -> Optional[Portfolio]:
        """Get portfolio by ID"""
        return self.db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()

    def get_user_portfolios(self, user_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[Portfolio]:
        """Get user's portfolios"""
        return self.db.query(Portfolio).filter(
            Portfolio.user_id == user_id
        ).offset(skip).limit(limit).all()

    def create_portfolio(self, user_id: uuid.UUID, portfolio_create: PortfolioCreate) -> Portfolio:
        """Create new portfolio"""
        db_portfolio = Portfolio(
            user_id=user_id,
            name=portfolio_create.name,
            description=portfolio_create.description,
            strategy_type=portfolio_create.strategy_type,
            initial_capital=portfolio_create.initial_capital or Decimal('0')
        )
        
        self.db.add(db_portfolio)
        self.db.commit()
        self.db.refresh(db_portfolio)
        return db_portfolio

    def update_portfolio(self, portfolio_id: uuid.UUID, portfolio_update: PortfolioUpdate) -> Optional[Portfolio]:
        """Update portfolio"""
        db_portfolio = self.get_portfolio(portfolio_id)
        if not db_portfolio:
            return None

        update_data = portfolio_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_portfolio, field, value)

        self.db.commit()
        self.db.refresh(db_portfolio)
        return db_portfolio

    def delete_portfolio(self, portfolio_id: uuid.UUID) -> bool:
        """Delete portfolio"""
        db_portfolio = self.get_portfolio(portfolio_id)
        if not db_portfolio:
            return False

        self.db.delete(db_portfolio)
        self.db.commit()
        return True

    def get_transactions(self, portfolio_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[Transaction]:
        """Get portfolio transactions"""
        return self.db.query(Transaction).filter(
            Transaction.portfolio_id == portfolio_id
        ).order_by(Transaction.trade_date.desc()).offset(skip).limit(limit).all()

    def create_transaction(self, portfolio_id: uuid.UUID, transaction_create: TransactionCreate) -> Transaction:
        """Create new transaction"""
        db_transaction = Transaction(
            portfolio_id=portfolio_id,
            index_code=transaction_create.index_code,
            index_name=transaction_create.index_name,
            transaction_type=transaction_create.transaction_type,
            price=transaction_create.price,
            quantity=transaction_create.quantity,
            fee=transaction_create.fee or Decimal('0'),
            tax=transaction_create.tax or Decimal('0'),
            total_amount=transaction_create.total_amount,
            note=transaction_create.note,
            trade_date=transaction_create.trade_date
        )
        
        self.db.add(db_transaction)
        
        # Update position
        self._update_position_after_transaction(db_transaction)
        
        self.db.commit()
        self.db.refresh(db_transaction)
        return db_transaction

    def update_transaction(self, transaction_id: uuid.UUID, transaction_update: TransactionUpdate) -> Optional[Transaction]:
        """Update transaction"""
        db_transaction = self.db.query(Transaction).filter(Transaction.id == transaction_id).first()
        if not db_transaction:
            return None

        # Store old values for position recalculation
        old_values = {
            'index_code': db_transaction.index_code,
            'transaction_type': db_transaction.transaction_type,
            'quantity': db_transaction.quantity,
            'price': db_transaction.price,
            'fee': db_transaction.fee,
            'tax': db_transaction.tax
        }

        update_data = transaction_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_transaction, field, value)

        # Recalculate position
        self._recalculate_position_after_transaction_update(db_transaction, old_values)
        
        self.db.commit()
        self.db.refresh(db_transaction)
        return db_transaction

    def delete_transaction(self, transaction_id: uuid.UUID) -> bool:
        """Delete transaction"""
        db_transaction = self.db.query(Transaction).filter(Transaction.id == transaction_id).first()
        if not db_transaction:
            return False

        # Reverse position changes
        self._reverse_position_after_transaction_delete(db_transaction)
        
        self.db.delete(db_transaction)
        self.db.commit()
        return True

    def get_fund_records(self, portfolio_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[FundRecord]:
        """Get portfolio fund records"""
        return self.db.query(FundRecord).filter(
            FundRecord.portfolio_id == portfolio_id
        ).order_by(FundRecord.record_date.desc()).offset(skip).limit(limit).all()

    def create_fund_record(self, portfolio_id: uuid.UUID, fund_record_create: FundRecordCreate) -> FundRecord:
        """Create new fund record"""
        db_fund_record = FundRecord(
            portfolio_id=portfolio_id,
            record_type=fund_record_create.record_type,
            amount=fund_record_create.amount,
            note=fund_record_create.note,
            record_date=fund_record_create.record_date
        )
        
        self.db.add(db_fund_record)
        self.db.commit()
        self.db.refresh(db_fund_record)
        return db_fund_record

    def get_positions(self, portfolio_id: uuid.UUID) -> List[Position]:
        """Get portfolio positions"""
        return self.db.query(Position).filter(
            Position.portfolio_id == portfolio_id,
            Position.quantity > 0
        ).all()

    def get_all_positions(self, portfolio_id: uuid.UUID) -> List[Position]:
        """Get all portfolio positions including zero quantity"""
        return self.db.query(Position).filter(
            Position.portfolio_id == portfolio_id
        ).all()

    def calculate_portfolio_statistics(self, portfolio_id: uuid.UUID) -> PortfolioStatistics:
        """Calculate portfolio statistics"""
        portfolio = self.get_portfolio(portfolio_id)
        if not portfolio:
            raise ValueError("Portfolio not found")

        # Get all positions
        positions = self.get_positions(portfolio_id)
        
        # Calculate total cost and market value
        total_cost = sum(pos.total_cost for pos in positions)
        # Note: For real market value, you would need current prices
        # For now, we'll use cost as placeholder
        total_market_value = total_cost  # This should be calculated with current prices
        
        # Calculate profit/loss
        total_profit_loss = total_market_value - total_cost
        total_return_rate = (total_profit_loss / total_cost * 100) if total_cost > 0 else Decimal('0')
        
        # Calculate available cash
        total_deposits = self.db.query(func.sum(FundRecord.amount)).filter(
            and_(FundRecord.portfolio_id == portfolio_id, FundRecord.record_type == 'deposit')
        ).scalar() or Decimal('0')
        
        total_withdrawals = self.db.query(func.sum(FundRecord.amount)).filter(
            and_(FundRecord.portfolio_id == portfolio_id, FundRecord.record_type == 'withdrawal')
        ).scalar() or Decimal('0')
        
        total_spent = self.db.query(func.sum(Transaction.total_amount)).filter(
            and_(Transaction.portfolio_id == portfolio_id, Transaction.transaction_type == 'buy')
        ).scalar() or Decimal('0')
        
        total_received = self.db.query(func.sum(Transaction.total_amount)).filter(
            and_(Transaction.portfolio_id == portfolio_id, Transaction.transaction_type == 'sell')
        ).scalar() or Decimal('0')
        
        available_cash = portfolio.initial_capital + total_deposits - total_withdrawals - total_spent + total_received
        total_assets = available_cash + total_market_value
        
        return PortfolioStatistics(
            total_market_value=total_market_value,
            total_cost=total_cost,
            total_profit_loss=total_profit_loss,
            total_return_rate=total_return_rate,
            available_cash=available_cash,
            total_assets=total_assets,
            position_count=len(positions)
        )

    def _update_position_after_transaction(self, transaction: Transaction):
        """Update position after transaction"""
        position = self.db.query(Position).filter(
            and_(
                Position.portfolio_id == transaction.portfolio_id,
                Position.index_code == transaction.index_code
            )
        ).first()

        if not position:
            position = Position(
                portfolio_id=transaction.portfolio_id,
                index_code=transaction.index_code,
                index_name=transaction.index_name,
                quantity=0,
                avg_cost=Decimal('0'),
                total_cost=Decimal('0')
            )
            self.db.add(position)

        if transaction.transaction_type == 'buy':
            # Calculate new average cost
            new_total_cost = position.total_cost + transaction.total_amount
            new_quantity = position.quantity + transaction.quantity
            new_avg_cost = new_total_cost / new_quantity if new_quantity > 0 else Decimal('0')
            
            position.quantity = new_quantity
            position.avg_cost = new_avg_cost
            position.total_cost = new_total_cost
            
        elif transaction.transaction_type == 'sell':
            # Reduce position
            position.quantity -= transaction.quantity
            if position.quantity <= 0:
                position.quantity = 0
                position.avg_cost = Decimal('0')
                position.total_cost = Decimal('0')
            else:
                position.total_cost = position.avg_cost * position.quantity

    def _recalculate_position_after_transaction_update(self, transaction: Transaction, old_values: dict):
        """Recalculate position after transaction update"""
        # This is a simplified version - in a real system, you might want to
        # recalculate all positions for the index to ensure accuracy
        position = self.db.query(Position).filter(
            and_(
                Position.portfolio_id == transaction.portfolio_id,
                Position.index_code == transaction.index_code
            )
        ).first()

        if position:
            # For simplicity, we'll recalculate from all transactions
            self._recalculate_position_from_transactions(position)

    def _reverse_position_after_transaction_delete(self, transaction: Transaction):
        """Reverse position changes after transaction delete"""
        position = self.db.query(Position).filter(
            and_(
                Position.portfolio_id == transaction.portfolio_id,
                Position.index_code == transaction.index_code
            )
        ).first()

        if position:
            # For simplicity, we'll recalculate from all remaining transactions
            self._recalculate_position_from_transactions(position)

    def _recalculate_position_from_transactions(self, position: Position):
        """Recalculate position from all transactions"""
        transactions = self.db.query(Transaction).filter(
            and_(
                Transaction.portfolio_id == position.portfolio_id,
                Transaction.index_code == position.index_code
            )
        ).order_by(Transaction.trade_date).all()

        quantity = 0
        total_cost = Decimal('0')

        for trans in transactions:
            if trans.transaction_type == 'buy':
                quantity += trans.quantity
                total_cost += trans.total_amount
            elif trans.transaction_type == 'sell':
                quantity -= trans.quantity

        position.quantity = max(0, quantity)
        position.total_cost = total_cost if position.quantity > 0 else Decimal('0')
        position.avg_cost = total_cost / position.quantity if position.quantity > 0 else Decimal('0')
