# Python 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 虚拟环境
venv/
.env/
.venv/
env/
pythonenv*/
*.venv

# 环境变量和配置文件
.env
*.env
*.env.*
!.env.example  # 保留示例环境文件

# 依赖管理
Pipfile.lock
poetry.lock  # 如果使用 Poetry

# 构建和分发文件
dist/
build/
*.egg
*.egg-info/
*.whl

# 测试和覆盖率报告
.coverage
coverage.*
htmlcov/
.pytest_cache/
.tox/
.nox/

# IDE 和编辑器文件
.idea/  # PyCharm
.vscode/  # Visual Studio Code
*.sublime-project
*.sublime-workspace

# Jupyter Notebook 检查点
.ipynb_checkpoints/

# 日志文件
*.log
logs/

# 数据库文件
*.sqlite3
*.db

# 临时文件和备份
*.bak
*.swp
*~
.DS_Store  # macOS 系统文件

# 敏感文件
*.pem
*.key
*.crt
*.secret

# 项目特定缓存
.cache/
*.cache

# 其他常见忽略
node_modules/  # 如果项目包含前端代码
*.min.js
*.min.css