#!/usr/bin/env python3
"""
Simple API test script
"""

import requests
import json
from datetime import date

API_BASE = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("Testing health endpoint...")
    response = requests.get(f"{API_BASE}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 200

def test_login():
    """Test login with default admin user"""
    print("\nTesting login...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    response = requests.post(f"{API_BASE}/api/v1/auth/login", json=login_data)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        token_data = response.json()
        print(f"Login successful! Token: {token_data['access_token'][:50]}...")
        return token_data['access_token']
    else:
        print(f"Login failed: {response.text}")
        return None

def test_user_info(token):
    """Test getting current user info"""
    print("\nTesting user info...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_BASE}/api/v1/auth/me", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        user_data = response.json()
        print(f"User: {user_data['username']} (ID: {user_data['id']})")
        return user_data
    else:
        print(f"Failed to get user info: {response.text}")
        return None

def test_create_portfolio(token):
    """Test creating a portfolio"""
    print("\nTesting portfolio creation...")
    headers = {"Authorization": f"Bearer {token}"}
    portfolio_data = {
        "name": "Test Portfolio",
        "description": "A test portfolio for API testing",
        "strategy_type": "index_investing",
        "initial_capital": 10000.00
    }
    response = requests.post(f"{API_BASE}/api/v1/portfolios/", json=portfolio_data, headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        portfolio = response.json()
        print(f"Portfolio created: {portfolio['name']} (ID: {portfolio['id']})")
        return portfolio
    else:
        print(f"Failed to create portfolio: {response.text}")
        return None

def test_create_transaction(token, portfolio_id):
    """Test creating a transaction"""
    print("\nTesting transaction creation...")
    headers = {"Authorization": f"Bearer {token}"}
    transaction_data = {
        "index_code": "000300",
        "index_name": "沪深300",
        "transaction_type": "buy",
        "price": 4.50,
        "quantity": 1000,
        "fee": 5.00,
        "tax": 0.00,
        "total_amount": 4505.00,
        "note": "Test transaction",
        "trade_date": str(date.today())
    }
    response = requests.post(
        f"{API_BASE}/api/v1/portfolios/{portfolio_id}/transactions", 
        json=transaction_data, 
        headers=headers
    )
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        transaction = response.json()
        print(f"Transaction created: {transaction['index_code']} - {transaction['transaction_type']}")
        return transaction
    else:
        print(f"Failed to create transaction: {response.text}")
        return None

def test_get_portfolios(token):
    """Test getting portfolios"""
    print("\nTesting get portfolios...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_BASE}/api/v1/portfolios/", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        portfolios = response.json()
        print(f"Found {len(portfolios)} portfolios")
        for portfolio in portfolios:
            print(f"  - {portfolio['name']} (ID: {portfolio['id']})")
        return portfolios
    else:
        print(f"Failed to get portfolios: {response.text}")
        return None

def main():
    """Run all tests"""
    print("=== API Test Suite ===")
    
    # Test health
    if not test_health():
        print("Health check failed. Is the server running?")
        return
    
    # Test login
    token = test_login()
    if not token:
        print("Login failed. Cannot continue with authenticated tests.")
        return
    
    # Test user info
    user = test_user_info(token)
    if not user:
        print("Failed to get user info.")
        return
    
    # Test portfolio creation
    portfolio = test_create_portfolio(token)
    if not portfolio:
        print("Failed to create portfolio.")
        return
    
    # Test transaction creation
    transaction = test_create_transaction(token, portfolio['id'])
    if not transaction:
        print("Failed to create transaction.")
        return
    
    # Test getting portfolios
    portfolios = test_get_portfolios(token)
    if not portfolios:
        print("Failed to get portfolios.")
        return
    
    print("\n=== All tests completed successfully! ===")

if __name__ == "__main__":
    main()
