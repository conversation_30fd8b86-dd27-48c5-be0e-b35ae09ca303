#!/usr/bin/env python3
"""Create admin user"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash
import uuid

def create_admin_user():
    db = SessionLocal()
    try:
        # Check if admin user exists
        existing_user = db.query(User).filter(User.username == "admin").first()
        if existing_user:
            print("Admin user already exists")
            return

        # Create admin user
        password_hash = get_password_hash("admin123")
        admin_user = User(
            id=uuid.uuid4(),
            username="admin",
            email="<EMAIL>",
            password_hash=password_hash,
            is_active=True,
            is_superuser=True
        )

        db.add(admin_user)
        db.commit()
        print("Admin user created successfully")
        print(f"Username: admin")
        print(f"Password: admin123")

    except Exception as e:
        print(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
