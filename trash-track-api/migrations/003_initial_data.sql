-- 初始化雪球风格投资组合数据
-- 包含三级分类体系和示例投资品种

-- 插入三级分类数据
-- 一级分类
INSERT INTO security_categories (id, name, parent_id, level, sort_order, description) VALUES
('11111111-1111-1111-1111-111111111111', '股票类', NULL, 1, 1, '股票及相关衍生品'),
('22222222-2222-2222-2222-222222222222', '基金类', NULL, 1, 2, '各类投资基金'),
('33333333-3333-3333-3333-333333333333', '债券类', NULL, 1, 3, '债券及固定收益产品'),
('44444444-4444-4444-4444-444444444444', '商品类', NULL, 1, 4, '大宗商品及贵金属'),
('55555555-5555-5555-5555-555555555555', '货币类', NULL, 1, 5, '货币市场工具');

-- 二级分类 - 股票类
INSERT INTO security_categories (id, name, parent_id, level, sort_order, description) VALUES
('11111111-1111-1111-1111-111111111112', 'A股', '11111111-1111-1111-1111-111111111111', 2, 1, '中国A股市场'),
('11111111-1111-1111-1111-111111111113', '港股', '11111111-1111-1111-1111-111111111111', 2, 2, '香港股票市场'),
('11111111-1111-1111-1111-111111111114', '美股', '11111111-1111-1111-1111-111111111111', 2, 3, '美国股票市场');

-- 二级分类 - 基金类
INSERT INTO security_categories (id, name, parent_id, level, sort_order, description) VALUES
('22222222-2222-2222-2222-222222222223', 'ETF基金', '22222222-2222-2222-2222-222222222222', 2, 1, '交易型开放式指数基金'),
('22222222-2222-2222-2222-222222222224', '主动基金', '22222222-2222-2222-2222-222222222222', 2, 2, '主动管理型基金'),
('22222222-2222-2222-2222-222222222225', '指数基金', '22222222-2222-2222-2222-222222222222', 2, 3, '被动指数跟踪基金');

-- 三级分类 - ETF基金
INSERT INTO security_categories (id, name, parent_id, level, sort_order, description) VALUES
('22222222-2222-2222-2222-222222222226', '宽基ETF', '22222222-2222-2222-2222-222222222223', 3, 1, '跟踪宽基指数的ETF'),
('22222222-2222-2222-2222-222222222227', '行业ETF', '22222222-2222-2222-2222-222222222223', 3, 2, '跟踪特定行业的ETF'),
('22222222-2222-2222-2222-222222222228', '主题ETF', '22222222-2222-2222-2222-222222222223', 3, 3, '跟踪特定主题的ETF'),
('22222222-2222-2222-2222-222222222229', '海外ETF', '22222222-2222-2222-2222-222222222223', 3, 4, '跟踪海外市场的ETF'),
('22222222-2222-2222-2222-22222222222A', '商品ETF', '22222222-2222-2222-2222-222222222223', 3, 5, '跟踪商品价格的ETF');

-- 插入示例投资品种（基于雪球数据）
INSERT INTO securities (id, symbol, name, market, category, currency) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SH512880', '证券ETF', 'SH', 'ETF', 'CNY'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'SH513180', '恒生科技指数ETF', 'SH', 'ETF', 'CNY'),
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'SZ159920', '恒生ETF', 'SZ', 'ETF', 'CNY'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'SH512980', '传媒ETF', 'SH', 'ETF', 'CNY'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'SH513050', '中概互联网ETF', 'SH', 'ETF', 'CNY'),
('ffffffff-ffff-ffff-ffff-ffffffffffff', 'SZ159938', '医药卫生ETF', 'SZ', 'ETF', 'CNY'),
('a1111111-1111-1111-1111-111111111111', 'SH515180', '红利ETF易方达', 'SH', 'ETF', 'CNY'),
('a2222222-2222-2222-2222-222222222222', 'SH513500', '标普500ETF', 'SH', 'ETF', 'CNY'),
('a3333333-3333-3333-3333-333333333333', 'SZ159941', '纳指ETF', 'SZ', 'ETF', 'CNY'),
('a4444444-4444-4444-4444-444444444444', 'SH588000', '科创50ETF', 'SH', 'ETF', 'CNY');

-- 建立投资品种与分类的关联关系
-- 证券ETF -> 行业ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '22222222-2222-2222-2222-222222222227');

-- 恒生科技指数ETF -> 海外ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '22222222-2222-2222-2222-222222222229');

-- 恒生ETF -> 海外ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('cccccccc-cccc-cccc-cccc-cccccccccccc', '22222222-2222-2222-2222-222222222229');

-- 传媒ETF -> 行业ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', '22222222-2222-2222-2222-222222222227');

-- 中概互联网ETF -> 主题ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '22222222-2222-2222-2222-222222222228');

-- 医药卫生ETF -> 行业ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('ffffffff-ffff-ffff-ffff-ffffffffffff', '22222222-2222-2222-2222-222222222227');

-- 红利ETF -> 主题ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('a1111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222228');

-- 标普500ETF -> 海外ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('a2222222-2222-2222-2222-222222222222', '22222222-2222-2222-2222-222222222229');

-- 纳指ETF -> 海外ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('a3333333-3333-3333-3333-333333333333', '22222222-2222-2222-2222-222222222229');

-- 科创50ETF -> 宽基ETF
INSERT INTO security_category_mappings (security_id, category_id) VALUES
('a4444444-4444-4444-4444-444444444444', '22222222-2222-2222-2222-222222222226');

-- 插入示例价格数据
INSERT INTO price_history (security_id, price, change_amount, change_rate, timestamp, source) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 0.8520, 0.0120, 1.43, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 0.6890, -0.0080, -1.15, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('cccccccc-cccc-cccc-cccc-cccccccccccc', 2.1450, 0.0230, 1.08, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', 0.7320, -0.0050, -0.68, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 0.8910, 0.0180, 2.06, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('ffffffff-ffff-ffff-ffff-ffffffffffff', 1.2340, 0.0090, 0.73, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('a1111111-1111-1111-1111-111111111111', 1.0560, -0.0020, -0.19, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('a2222222-2222-2222-2222-222222222222', 5.2180, 0.0340, 0.66, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('a3333333-3333-3333-3333-333333333333', 4.8920, 0.0280, 0.58, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual'),
('a4444444-4444-4444-4444-444444444444', 1.4560, 0.0110, 0.76, EXTRACT(EPOCH FROM NOW()) * 1000, 'manual');

-- 创建示例投资组合
INSERT INTO portfolios (id, user_id, name, description, strategy_type, principal, cash, total_assets) VALUES
('99999999-9999-9999-9999-999999999999', 
 (SELECT id FROM users WHERE username = 'admin' LIMIT 1),
 '我的ETF组合', 
 '基于雪球数据的ETF投资组合', 
 '指数投资', 
 160000.00, 
 5500.00, 
 160000.00);

-- 插入示例仓位管理规则
INSERT INTO position_rules (portfolio_id, category_id, min_weight, max_weight, target_weight) VALUES
-- 基金类最大80%
('99999999-9999-9999-9999-999999999999', '22222222-2222-2222-2222-222222222222', 0.0000, 0.8000, 0.7000),
-- ETF基金最大70%
('99999999-9999-9999-9999-999999999999', '22222222-2222-2222-2222-222222222223', 0.0000, 0.7000, 0.6000),
-- 海外ETF最大30%
('99999999-9999-9999-9999-999999999999', '22222222-2222-2222-2222-222222222229', 0.0000, 0.3000, 0.2000),
-- 行业ETF最大40%
('99999999-9999-9999-9999-999999999999', '22222222-2222-2222-2222-222222222227', 0.0000, 0.4000, 0.3000);
