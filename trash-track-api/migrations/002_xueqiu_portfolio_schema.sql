-- 雪球风格投资组合数据库结构重新设计
-- 基于雪球数据分析结果，优化数据表结构

-- 删除旧的表结构（保留用户相关表）
DROP TABLE IF EXISTS alert_history CASCADE;
DROP TABLE IF EXISTS alert_rules CASCADE;
DROP TABLE IF EXISTS user_watchlist CASCADE;
DROP TABLE IF EXISTS fundamental_data CASCADE;
DROP TABLE IF EXISTS kline_data CASCADE;
DROP TABLE IF EXISTS positions CASCADE;
DROP TABLE IF EXISTS fund_records CASCADE;
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS portfolios CASCADE;
DROP TABLE IF EXISTS indices CASCADE;

-- 1. 投资品种表（对应雪球的symbol）
CREATE TABLE IF NOT EXISTS securities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) UNIQUE NOT NULL,  -- 如 SH513500, SZ159941
    name VARCHAR(100) NOT NULL,          -- 如 标普500ETF, 纳指ETF
    market VARCHAR(10) NOT NULL,         -- SH, SZ, HK, US
    category VARCHAR(50),                -- ETF, 股票, 基金等
    currency VARCHAR(10) DEFAULT 'CNY', -- CNY, USD, HKD
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. 投资组合表（重新设计）
CREATE TABLE IF NOT EXISTS portfolios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    strategy_type VARCHAR(50),
    -- 雪球风格的组合统计字段
    total_assets DECIMAL(15, 2) DEFAULT 0,      -- 总资产
    principal DECIMAL(15, 2) DEFAULT 0,         -- 本金
    cash DECIMAL(15, 2) DEFAULT 0,              -- 现金
    market_value DECIMAL(15, 2) DEFAULT 0,      -- 市值
    accum_amount DECIMAL(15, 2) DEFAULT 0,      -- 累计收益金额
    accum_rate DECIMAL(8, 4) DEFAULT 0,         -- 累计收益率
    day_float_amount DECIMAL(15, 2) DEFAULT 0,  -- 日浮动金额
    day_float_rate DECIMAL(8, 4) DEFAULT 0,     -- 日浮动率
    currency VARCHAR(10) DEFAULT 'CNY',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. 持仓表（对应雪球的list数据）
CREATE TABLE IF NOT EXISTS positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    security_id UUID NOT NULL REFERENCES securities(id) ON DELETE CASCADE,
    -- 持仓基本信息
    shares DECIMAL(15, 2) NOT NULL DEFAULT 0,           -- 持股数量
    current_price DECIMAL(10, 4) DEFAULT 0,             -- 当前价格
    change_amount DECIMAL(10, 4) DEFAULT 0,             -- 价格变动金额
    change_rate DECIMAL(8, 4) DEFAULT 0,                -- 价格变动率
    -- 成本信息
    diluted_cost DECIMAL(10, 4) DEFAULT 0,              -- 摊薄成本
    hold_cost DECIMAL(10, 4) DEFAULT 0,                 -- 持仓成本
    -- 市值和收益
    market_value DECIMAL(15, 2) DEFAULT 0,              -- 市值
    float_amount DECIMAL(15, 2) DEFAULT 0,              -- 浮动盈亏金额
    float_rate DECIMAL(8, 4) DEFAULT 0,                 -- 浮动盈亏率
    accum_amount DECIMAL(15, 2) DEFAULT 0,              -- 累计收益金额
    accum_rate DECIMAL(8, 4) DEFAULT 0,                 -- 累计收益率
    day_float_amount DECIMAL(15, 2) DEFAULT 0,          -- 日浮动金额
    day_float_rate DECIMAL(8, 4) DEFAULT 0,             -- 日浮动率
    -- 时间信息
    open_time BIGINT,                                   -- 开仓时间戳
    liquidation_time BIGINT DEFAULT 0,                  -- 清仓时间戳（0表示未清仓）
    delay_remark VARCHAR(255) DEFAULT '',               -- 延迟备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(portfolio_id, security_id)
);

-- 4. 交易记录表（对应雪球的transactions）
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    security_id UUID NOT NULL REFERENCES securities(id) ON DELETE CASCADE,
    -- 雪球原始字段
    tid BIGINT,                                         -- 雪球交易ID
    transaction_type INTEGER NOT NULL,                  -- 1=买入, 2=卖出, 9=除权除息
    type_name VARCHAR(20) NOT NULL,                     -- 买入, 卖出, 除权除息
    shares DECIMAL(15, 2) NOT NULL,                     -- 交易股数
    price DECIMAL(10, 4) NOT NULL,                      -- 交易价格
    amount DECIMAL(15, 2) NOT NULL,                     -- 交易金额
    -- 费用信息
    commission DECIMAL(10, 2),                          -- 佣金
    tax DECIMAL(10, 2),                                 -- 税费
    commission_rate DECIMAL(8, 4),                      -- 佣金率
    tax_rate DECIMAL(8, 4),                             -- 税率
    -- 除权除息相关
    unit_dividend DECIMAL(10, 4),                       -- 单位股息
    unit_shares DECIMAL(15, 2),                         -- 单位股数
    unit_increase_shares DECIMAL(15, 2),                -- 单位增股
    record_date BIGINT,                                 -- 除权除息登记日
    -- 其他信息
    comment TEXT,                                       -- 备注
    description TEXT,                                   -- 描述
    trade_time BIGINT NOT NULL,                         -- 交易时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. 转账记录表（对应雪球的bank_transfers）
CREATE TABLE IF NOT EXISTS fund_transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    -- 雪球原始字段
    tid BIGINT,                                         -- 雪球转账ID
    transfer_type INTEGER NOT NULL,                     -- 1=转入, 2=转出
    market VARCHAR(10) NOT NULL,                        -- CHA, US, HK
    amount DECIMAL(15, 2) NOT NULL,                     -- 转账金额
    transfer_time BIGINT NOT NULL,                      -- 转账时间戳
    note TEXT,                                          -- 备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 6. 三级分类表
CREATE TABLE IF NOT EXISTS security_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    parent_id UUID REFERENCES security_categories(id) ON DELETE CASCADE,
    level INTEGER NOT NULL CHECK (level IN (1, 2, 3)), -- 1=一级, 2=二级, 3=三级
    sort_order INTEGER DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 7. 投资品种分类关联表
CREATE TABLE IF NOT EXISTS security_category_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    security_id UUID NOT NULL REFERENCES securities(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES security_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(security_id, category_id)
);

-- 8. 仓位管理规则表
CREATE TABLE IF NOT EXISTS position_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES security_categories(id) ON DELETE CASCADE,
    min_weight DECIMAL(5, 4) DEFAULT 0,                 -- 最小仓位权重 (0-1)
    max_weight DECIMAL(5, 4) DEFAULT 1,                 -- 最大仓位权重 (0-1)
    target_weight DECIMAL(5, 4),                        -- 目标仓位权重 (0-1)
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(portfolio_id, category_id)
);

-- 9. 价格历史表（用于实时价格更新）
CREATE TABLE IF NOT EXISTS price_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    security_id UUID NOT NULL REFERENCES securities(id) ON DELETE CASCADE,
    price DECIMAL(10, 4) NOT NULL,
    change_amount DECIMAL(10, 4) DEFAULT 0,
    change_rate DECIMAL(8, 4) DEFAULT 0,
    volume BIGINT DEFAULT 0,
    timestamp BIGINT NOT NULL,                          -- 价格时间戳
    source VARCHAR(50) DEFAULT 'manual',                -- 数据源
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_securities_symbol ON securities(symbol);
CREATE INDEX IF NOT EXISTS idx_securities_market ON securities(market);
CREATE INDEX IF NOT EXISTS idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_positions_portfolio_id ON positions(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_positions_security_id ON positions(security_id);
CREATE INDEX IF NOT EXISTS idx_transactions_portfolio_id ON transactions(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_transactions_security_id ON transactions(security_id);
CREATE INDEX IF NOT EXISTS idx_transactions_trade_time ON transactions(trade_time);
CREATE INDEX IF NOT EXISTS idx_fund_transfers_portfolio_id ON fund_transfers(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_fund_transfers_transfer_time ON fund_transfers(transfer_time);
CREATE INDEX IF NOT EXISTS idx_security_categories_parent_id ON security_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_security_categories_level ON security_categories(level);
CREATE INDEX IF NOT EXISTS idx_price_history_security_id ON price_history(security_id);
CREATE INDEX IF NOT EXISTS idx_price_history_timestamp ON price_history(timestamp);

-- 创建更新时间触发器
CREATE TRIGGER update_securities_updated_at BEFORE UPDATE ON securities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_portfolios_updated_at BEFORE UPDATE ON portfolios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON positions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_security_categories_updated_at BEFORE UPDATE ON security_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_position_rules_updated_at BEFORE UPDATE ON position_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
