version: "3.8"

services:
    # PostgreSQL Database
    postgres:
        image: postgres:16-alpine
        container_name: index_investing_postgres
        environment:
            POSTGRES_DB: index_investing
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: postgres123
            POSTGRES_HOST_AUTH_METHOD: trust
        ports:
            - "5432:5432"
        volumes:
            - postgres_data:/var/lib/postgresql/data
            - ./trash-track-api/init.sql:/docker-entrypoint-initdb.d/init.sql
        networks:
            - index_investing_network
        restart: unless-stopped

    # Redis Cache
    redis:
        image: redis:7-alpine
        container_name: index_investing_redis
        ports:
            - "6379:6379"
        volumes:
            - redis_data:/data
        networks:
            - index_investing_network
        restart: unless-stopped
        command: redis-server --appendonly yes

    # FastAPI Backend (optional, for production)
    backend:
        build:
            context: ./trash-track-api
            dockerfile: Dockerfile
        container_name: index_investing_backend
        environment:
            - DATABASE_URL=***********************************************/index_investing
            - REDIS_URL=redis://redis:6379/0
            - SECRET_KEY=your-secret-key-change-in-production
            - ALGORITHM=HS256
            - ACCESS_TOKEN_EXPIRE_MINUTES=30
        ports:
            - "8000:8000"
        depends_on:
            - postgres
            - redis
        networks:
            - index_investing_network
        restart: unless-stopped
        volumes:
            - ./trash-track-api:/app
        profiles:
            - production

    # Next.js Frontend (optional, for production)
    frontend:
        build:
            context: ./trash-track-web
            dockerfile: Dockerfile
        container_name: index_investing_frontend
        environment:
            - NEXT_PUBLIC_API_URL=http://backend:8000
        ports:
            - "3000:3000"
        depends_on:
            - backend
        networks:
            - index_investing_network
        restart: unless-stopped
        volumes:
            - ./trash-track-web:/app
        profiles:
            - production

volumes:
    postgres_data:
    redis_data:

networks:
    index_investing_network:
        driver: bridge
