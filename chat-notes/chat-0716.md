第一步先通读一下该项目，充分理解该项目的结构，使用的技术栈，UI 风格，编码风格等
第二步启动该项目，保证项目可以正常启动并运行，页面正常访问
第三步帮我实现以下功能
1、原项目中有一个组合的功能但是功能不够完善，没有跟持仓结合起来，不符合我的预期，可以删掉
2、实现雪球的持仓组合功能，主要 UI 如文件@xueqiu/UI-持仓主界面.png, @xueqiu/UI-交易记录主界面.png, @xueqiu/UI-转账记录主界面.png, @xueqiu/UI-转出.png, @xueqiu/UI-转入.png, @xueqiu/UI-买入.png, @xueqiu/UI-卖出.png,@xueqiu/UI-合股.png,@xueqiu/UI-拆股.png,@xueqiu/UI-除权除息.png
3、我提供了一下持仓相关数据文件供你参考,如:@xueqiu/DATA-组合-我的网格.json, @xueqiu/DATA-组合-E 大网格.json, @xueqiu/DATA-组合-我的 ETF.json, @xueqiu/DATA-交易记录-ALL.json, @xueqiu/DATA-转账记录-ALL.json, @xueqiu/DATA-交易记录-传媒.json

我有以下要求
1、实现界面上的所有功能，但不是完全克隆他的 UI，你需要基于现有项目的 UI 风格来重新设置 UI，要求美观，易用，贴合项目的 UI 风格
2、参考所有给你的 UI，在当前项目下实现对应的功能
3、基于给你的数据，设计优良的考虑拓展性维护性的数据表结构，先前项目的数据表并不是最优的，需要你来重新设计
4、这里涉及到一个品种当前价格实时更新的功能，数据源我后续会提供，你可以先实现整体逻辑。更新频率需要考虑可以进行动态设置
5、新增对品种的归类功能，目前计划有三级分类，后续要基于分类做仓位管理，比如某个分类仓位上下限设置，超过设置值需要触发预警通知等
6、第 5 点提到的分类以及分类的仓位上下限设置是否要单独出来管理，你基于前面的实现，在设计一下，要求考虑后续可维护性和拓展性等
