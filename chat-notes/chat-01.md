现在我已经把 frontend 的示例工程https://github.com/arhamkhnz/next-shadcn-admin-dashboard clone 下来了
请基于我的要求完成以下两个大模块的开发
特殊要求
frontend

1. 使用该示例进行二次开发，要求保留原来的菜单项供我参考对比，新增的功能只需要增加对应菜单和页面。
2. 开发语言，框架，UI 风格，依赖库等严格按照示例工程来，不能随意添加依赖库，必须要添加时，需要跟我确认。

backend

1. 按照需求规格来开发
2. 数据库方面缓存之类的基础服务，采用 docker 形式启动，需要用到的告诉我具体操作步骤

### 3.1 用户管理模块

#### 3.1.1 用户认证

-   **登录功能**：用户名/密码登录，支持记住登录状态
-   **会话管理**：JWT Token 认证，支持 Token 刷新
-   **权限控制**：基于角色的访问控制（RBAC）

### 3.3 仓位管理模块

#### 3.3.1 投资组合管理

-   **组合创建**：支持创建多个投资组合（动态平衡、网格策略等）
-   **组合配置**：设置组合名称、描述、投资策略参数
-   **组合切换**：快速切换不同组合视图

#### 3.3.2 交易记录管理

-   **交易类型**：买入、卖出、除权除息、拆合股
-   **交易详情**：价格、数量、手续费、印花税、备注
-   **批量导入**：支持 Excel 文件批量导入交易记录
-   **交易历史**：完整的交易历史记录和查询

#### 3.3.3 资金管理

-   **转账记录**：转入、转出资金记录
-   **资金流水**：详细的资金变动记录
-   **资金统计**：可用资金、冻结资金、总资产统计

#### 3.3.4 持仓分析

-   **实时持仓**：当前持仓股票和数量
-   **历史持仓**：已清仓股票的历史记录
-   **持仓成本**：平均成本、总成本计算
-   **盈亏分析**：浮动盈亏、累计盈亏、收益率计算

#### 3.3.5 统计报表

-   **总市值计算**：实时总市值更新
-   **今日盈亏**：当日盈亏统计
-   **总盈亏**：累计盈亏统计
-   **收益率分析**：总收益率、年化收益率
-   **风险指标**：最大回撤、夏普比率等

1、把原来 Dashboard 里面的菜单 Default CRM Finance 还原回来
2、user-management portfolio-management 路由不对，不应该接在 dashborad 下面，他们是一级路由
3、user-management portfolio-management 先要不要用折叠菜单，先平铺开，有归类分组就好，不需要的菜单先去掉

Investment Management, Portfolio Management, User Management 不够简洁，简短一点
组合管理 一级路由为 portfolio,组合页面路由为/portfolio/default
用户和登陆这块归属为 User 分组， 用户管理一级路由为 user，用户管理页面路由为/user/default
Dashboards 分组下 只保留 Default CRM Finance 这三个

把之前 Misc 分组加回来，分组命名为其他
前端工程中 portfolio-management 和 user-management 这两个目录要删掉，已经使用简短的 portfolio 和 user 代替了
把 UI 改成中文

frontend/src/app/(main)/portfolio-management/
frontend/src/app/(main)/user-management/
frontend/src/app/(main)/dashboard/portfolio-management/
frontend/src/app/(main)/dashboard/user-management/
这几个目录还是没删除

路由，组件，页面中命名要尽量简洁，比如避免 xx-management 这种长的命名，有这种命名的修改优化一下

1、投资组合页面和用户管理页面有一个问题，登陆后刷新，每次都会先出现登陆页面，然后再消失进到正常页面，这个体验不好 优化下
2、如果需要登陆，需要跳登陆页面，而不是右侧页面显示登陆页面 这部分也优化下
3、logout 功能实现下
4、菜单投资组合改成组合，用户管理改成用户
