# index-investing (指数投资)

## 项目背景

我是一名 A 股指数投资者，我想要实现这个系统来协助管理我的投资组合和分析指数数据，辅助我的投资决策。
我有不同的投资账号分别来践行和验证我的策略，所以需要不同的组合来跟踪，比如动态平衡组合，网格策略组合，短线投机组合等。
我需要跟踪指数的 K 线数据，基本面数据，所以我需要一个指数数据模块来获取并管理指数数据。然后基于这些数据我需要有一些指标预警来协助我的投资决策。
这些指标包括但不限于：K 线的 7080，均线，BBI，基本面的 PE，PE-Bands，PB 等等。

## 项目概述

本项目设想是基于前后端分离的设计模式。
后端（服务端）实现采用 python 语言进行实现，并提供 restful API 给前端使用。
前端（客户端）实现采用 typescript 语言进行实现，使用 next.js 框架提供用户界面和交互。前端模板参考该开源项目(https://github.com/arhamkhnz/next-shadcn-admin-dashboard)进行修改，要求必须贴合原设计。

## 核心功能

### 服务端

1. 使用 python 语言实现
2. 提供 restful API 给前端使用，具体使用什么框架你给我建议
3. 实现定时和手动拉取数据的功能（实现增量同步，全量同步），目前数据源暂定理杏仁，数据需考虑重复获取不用新增的问题，即重复获取只会更新数据，不会新增数据
4. 实现自定义指标的实时检查和预警，获取当日最新数据后检查指标预警情况，达到预警条件后发送通知（chrome 通知 和 telegram 通知）
5. 实现仓位管理功能，仓位管理支持添加组合，支持交易记录管理（买入，卖出，除权除息，拆合股，支持填入佣金税费备注等的录入），支持转账记录管理（转入，转出，支持备注），支持历史持仓查询（清仓的股票还能查到记录），实现总市值，今日盈亏，总盈亏，总收益率，浮动盈亏，累计盈亏等统计功能。【仓位管理可以参考雪球的模拟市值功能】
6. 实现用户管理，用户登陆（用户名密码登入即可），不用支持注册。指数数据属于系统基础数据不区分用户，但是对关注指数的指标预警管理，仓位管理等需要区分用户。

### 前端

1. 使用https://github.com/arhamkhnz/next-shadcn-admin-dashboard进行二次开发，要求保留原来的菜单项供我参考对比，新增的功能只需要增加对应菜单和页面。
2. 开发语言，框架，UI 风格，依赖库等严格按照(https://github.com/arhamkhnz/next-shadcn-admin-dashboard)这个项目来，不能随意添加依赖库，必须要添加时，需要跟我确认。
3. 实现仓位管理 UI 逻辑，要求 UI 符合模版项目风格，功能交互上要简单易用。
4. 实现指数数据管理 UI 逻辑，要求 UI 符合模版项目风格，功能交互上要简单易用。
5. 实现我的关注，包括，关注指数列表。然后指标预警管理需支持两种类型，一种是列表预警：即预警条件对关注列表里的所有指数有效。一种是指数预警：即预警条件只对单个指数有效。
6. 实现用户管理 UI 逻辑，要求 UI 符合模版项目风格，功能交互上要简单易用。
7. 实现仓位，指数，指标等的 Dashboard UI，要求 UI 美观，清晰。

## 技术实现

1. python API 框架选型未定，需要你帮我选定一个
2. 数据库选型未定，需要你帮我基于现有的需求选定一个合适的数据库

## 其他高级功能（可暂时不实现）

1. 交易策略管理，支持自定义交易策略，支持策略回测，支持策略执行
2. 交易策略回测，支持策略回测，支持策略回测结果分析
3. 交易策略优化，支持策略参数优化，支持策略参数优化结果分析

## 文件结构

```

├── api                          # 服务端项目目录，名称是暂定的，有更合适的命名可以帮我修改掉
├── client                       # 前端项目目录，名称是暂定的，有更合适的命名可以帮我修改掉
├── project-doc.md               # 项目需求文档初版
```

## 其他注意事项

1. 用户管理，用户登陆，仓位管理这几个不依赖外部数据的功能，可以优先实现
2. 指数数据管理，关注指数列表，指标预警，策略等可以先搭建模块框架，后面等数据源确定了再一一实现。
